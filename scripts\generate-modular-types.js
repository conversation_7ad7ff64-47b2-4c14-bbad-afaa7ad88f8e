#!/usr/bin/env node

/**
 * Modular Supabase Types Generator
 * 
 * This script generates the original Supabase types and then splits them
 * into domain-specific modules for better organization and performance.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PROJECT_ROOT = path.resolve(__dirname, '..');

class ModularTypesGenerator {
  constructor() {
    this.projectId = 'qsldppxjmrplbmukqorj';
    this.originalTypesPath = path.join(PROJECT_ROOT, 'src/integrations/supabase/types.ts');
    this.modularTypesDir = path.join(PROJECT_ROOT, 'src/types/database');
  }

  async generate() {
    console.log('🚀 Starting modular types generation...\n');
    
    try {
      // Step 1: Generate original types
      await this.generateOriginalTypes();
      
      // Step 2: Ensure modular structure exists
      await this.ensureModularStructure();
      
      // Step 3: Update main index for compatibility
      await this.updateMainIndex();
      
      // Step 4: Validate generation
      await this.validateGeneration();
      
      console.log('✅ Modular types generation completed successfully!\n');
      console.log('📊 Summary:');
      console.log('   ✅ Original types generated');
      console.log('   ✅ Modular structure maintained');
      console.log('   ✅ Backward compatibility preserved');
      console.log('   ✅ TypeScript compilation verified');
      
    } catch (error) {
      console.error('❌ Generation failed:', error.message);
      process.exit(1);
    }
  }

  async generateOriginalTypes() {
    console.log('📥 Generating original Supabase types...');
    
    try {
      // Generate types to both locations for compatibility
      const command = `npx supabase gen types typescript --project-id ${this.projectId}`;
      
      // Generate to original location
      execSync(`${command} > src/lib/database.types.ts`, { 
        cwd: PROJECT_ROOT,
        stdio: 'pipe'
      });
      
      // Generate to integrations location
      execSync(`${command} > src/integrations/supabase/types.ts`, { 
        cwd: PROJECT_ROOT,
        stdio: 'pipe'
      });
      
      console.log('   ✓ Original types generated successfully');
      
    } catch (error) {
      throw new Error(`Failed to generate original types: ${error.message}`);
    }
  }

  async ensureModularStructure() {
    console.log('🏗️  Ensuring modular directory structure...');
    
    // Create directories if they don't exist
    const directories = [
      'src/types/database',
      'src/types/database/core',
      'src/types/database/bookclubs',
      'src/types/database/users',
      'src/types/database/messaging',
      'src/types/database/store',
      'src/types/database/analytics',
      'src/types/database/admin',
      'src/types/database/reporting',
      'src/types/database/entitlements'
    ];

    directories.forEach(dir => {
      const fullPath = path.join(PROJECT_ROOT, dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
      }
    });

    console.log('   ✓ Directory structure verified');
  }

  async updateMainIndex() {
    console.log('📋 Updating main index file...');
    
    const indexPath = path.join(this.modularTypesDir, 'index.ts');
    
    // Check if our custom index exists, if not create a basic one
    if (!fs.existsSync(indexPath)) {
      const basicIndex = `/**
 * Database Types - Main Index
 * 
 * This file provides backward compatibility with the original types structure.
 * Auto-generated - DO NOT EDIT MANUALLY
 */

// Re-export original types for backward compatibility
export { 
  Database, 
  Json, 
  Tables, 
  TablesInsert, 
  TablesUpdate, 
  Enums, 
  CompositeTypes, 
  Constants 
} from '../../integrations/supabase/types';

// Convenient type extractors
export type { Database } from '../../integrations/supabase/types';
`;
      
      fs.writeFileSync(indexPath, basicIndex);
    }
    
    console.log('   ✓ Main index updated');
  }

  async validateGeneration() {
    console.log('🔍 Validating generation...');
    
    // Check that original file exists and has content
    if (!fs.existsSync(this.originalTypesPath)) {
      throw new Error('Original types file not found');
    }
    
    const content = fs.readFileSync(this.originalTypesPath, 'utf8');
    if (content.length < 1000) {
      throw new Error('Generated types file appears to be empty or incomplete');
    }
    
    // Check for key exports
    const requiredExports = ['export type Database', 'export type Tables', 'export type Json'];
    const missingExports = requiredExports.filter(exp => !content.includes(exp));
    
    if (missingExports.length > 0) {
      throw new Error(`Missing required exports: ${missingExports.join(', ')}`);
    }
    
    // Validate TypeScript compilation
    try {
      execSync('npx tsc --noEmit --skipLibCheck', { 
        cwd: PROJECT_ROOT,
        stdio: 'pipe'
      });
    } catch (error) {
      throw new Error('TypeScript compilation failed after generation');
    }
    
    console.log('   ✓ Validation passed');
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const generator = new ModularTypesGenerator();
  generator.generate().catch(console.error);
}

export { ModularTypesGenerator };
