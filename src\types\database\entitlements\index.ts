/**
 * ENTITLEMENTS Domain Types
 * 
 * Auto-generated from Supabase schema - DO NOT EDIT MANUALLY
 */

import { Json } from '../core/base';

export interface EntitlementsTables {
  payment_records: {
    Row: {
      id: string
      user_id: string
      amount: number
      currency: string | null
      payment_method: string | null
      transaction_id: string | null
      status: string | null
      created_at: string | null
      updated_at: string | null
      subscription_id: string | null
    }
    Insert: {
      id?: string
      user_id: string
      amount: number
      currency?: string | null
      payment_method?: string | null
      transaction_id?: string | null
      status?: string | null
      created_at?: string | null
      updated_at?: string | null
      subscription_id?: string | null
    }
    Update: {
      id?: string
      user_id?: string
      amount?: number
      currency?: string | null
      payment_method?: string | null
      transaction_id?: string | null
      status?: string | null
      created_at?: string | null
      updated_at?: string | null
      subscription_id?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "payment_records_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "payment_records_subscription_id_fkey"
        columns: ["subscription_id"]
        isOneToOne: false
        referencedRelation: "user_subscriptions"
        referencedColumns: ["id"]
      },
    ]
  }
}

export interface EntitlementsFunctions {
  calculate_subscription_end_date: {
    Args: {
      start_date: string
      subscription_type: string
    }
    Returns: string
  }
  create_subscription_with_payment: {
    Args: {
      user_id: string
      subscription_type: string
      amount: number
      payment_method: string
      transaction_id: string
    }
    Returns: string
  }
  get_active_subscription: {
    Args: {
      user_id: string
    }
    Returns: {
      id: string
      subscription_type: string
      status: string
      expires_at: string
    }[]
  }
  has_account_tier: {
    Args: {
      user_id: string
      required_tier: string
    }
    Returns: boolean
  }
  has_active_subscription: {
    Args: {
      user_id: string
    }
    Returns: boolean
  }
}
