/**
 * USERS Domain Types
 * 
 * Auto-generated from Supabase schema - DO NOT EDIT MANUALLY
 */

import { Json } from '../core/base';

export interface UsersTables {
  users: {
    Row: {
      id: string
      username: string | null
      display_name: string | null
      bio: string | null
      profile_photo_url: string | null
      created_at: string | null
      updated_at: string | null
      store_id: string | null
    }
    Insert: {
      id: string
      username?: string | null
      display_name?: string | null
      bio?: string | null
      profile_photo_url?: string | null
      created_at?: string | null
      updated_at?: string | null
      store_id?: string | null
    }
    Update: {
      id?: string
      username?: string | null
      display_name?: string | null
      bio?: string | null
      profile_photo_url?: string | null
      created_at?: string | null
      updated_at?: string | null
      store_id?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "users_store_id_fkey"
        columns: ["store_id"]
        isOneToOne: false
        referencedRelation: "stores"
        referencedColumns: ["id"]
      },
    ]
  }
  user_subscriptions: {
    Row: {
      id: string
      user_id: string
      subscription_type: string
      status: string
      created_at: string | null
      updated_at: string | null
      expires_at: string | null
    }
    Insert: {
      id?: string
      user_id: string
      subscription_type: string
      status: string
      created_at?: string | null
      updated_at?: string | null
      expires_at?: string | null
    }
    Update: {
      id?: string
      user_id?: string
      subscription_type?: string
      status?: string
      created_at?: string | null
      updated_at?: string | null
      expires_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "user_subscriptions_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  user_warnings: {
    Row: {
      id: string
      user_id: string
      warning_type: string
      message: string
      created_at: string | null
      resolved_at: string | null
    }
    Insert: {
      id?: string
      user_id: string
      warning_type: string
      message: string
      created_at?: string | null
      resolved_at?: string | null
    }
    Update: {
      id?: string
      user_id?: string
      warning_type?: string
      message?: string
      created_at?: string | null
      resolved_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "user_warnings_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  reading_list_items: {
    Row: {
      id: string
      user_id: string
      book_id: string
      status: string
      added_at: string | null
      started_at: string | null
      completed_at: string | null
      notes: string | null
    }
    Insert: {
      id?: string
      user_id: string
      book_id: string
      status: string
      added_at?: string | null
      started_at?: string | null
      completed_at?: string | null
      notes?: string | null
    }
    Update: {
      id?: string
      user_id?: string
      book_id?: string
      status?: string
      added_at?: string | null
      started_at?: string | null
      completed_at?: string | null
      notes?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "reading_list_items_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "reading_list_items_book_id_fkey"
        columns: ["book_id"]
        isOneToOne: false
        referencedRelation: "books"
        referencedColumns: ["id"]
      },
    ]
  }
}

export interface UsersFunctions {
  get_user_message_retention_days: {
    Args: {
      user_id: string
    }
    Returns: number
  }
  get_user_retention_info: {
    Args: {
      user_id: string
    }
    Returns: {
      retention_days: number
      subscription_type: string
    }[]
  }
  is_user_conversation_participant: {
    Args: {
      conversation_id: string
      user_id: string
    }
    Returns: boolean
  }
}
