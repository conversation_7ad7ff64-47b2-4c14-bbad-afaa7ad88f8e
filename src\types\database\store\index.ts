/**
 * STORE Domain Types
 * 
 * Auto-generated from Supabase schema - DO NOT EDIT MANUALLY
 */

import { Json } from '../core/base';

export interface StoreTables {
  stores: {
    Row: {
      id: string
      name: string
      description: string | null
      location: string | null
      contact_email: string | null
      contact_phone: string | null
      website_url: string | null
      logo_url: string | null
      created_at: string | null
      updated_at: string | null
    }
    Insert: {
      id?: string
      name: string
      description?: string | null
      location?: string | null
      contact_email?: string | null
      contact_phone?: string | null
      website_url?: string | null
      logo_url?: string | null
      created_at?: string | null
      updated_at?: string | null
    }
    Update: {
      id?: string
      name?: string
      description?: string | null
      location?: string | null
      contact_email?: string | null
      contact_phone?: string | null
      website_url?: string | null
      logo_url?: string | null
      created_at?: string | null
      updated_at?: string | null
    }
    Relationships: []
  }
  store_administrators: {
    Row: {
      id: string
      store_id: string
      user_id: string
      role: string
      created_at: string | null
    }
    Insert: {
      id?: string
      store_id: string
      user_id: string
      role: string
      created_at?: string | null
    }
    Update: {
      id?: string
      store_id?: string
      user_id?: string
      role?: string
      created_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "store_administrators_store_id_fkey"
        columns: ["store_id"]
        isOneToOne: false
        referencedRelation: "stores"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "store_administrators_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  store_carousel_items: {
    Row: {
      id: string
      store_id: string
      title: string
      description: string | null
      image_url: string | null
      link_url: string | null
      display_order: number | null
      is_active: boolean | null
      created_at: string | null
      updated_at: string | null
    }
    Insert: {
      id?: string
      store_id: string
      title: string
      description?: string | null
      image_url?: string | null
      link_url?: string | null
      display_order?: number | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
    }
    Update: {
      id?: string
      store_id?: string
      title?: string
      description?: string | null
      image_url?: string | null
      link_url?: string | null
      display_order?: number | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "store_carousel_items_store_id_fkey"
        columns: ["store_id"]
        isOneToOne: false
        referencedRelation: "stores"
        referencedColumns: ["id"]
      },
    ]
  }
  store_community_showcase: {
    Row: {
      id: string
      store_id: string
      title: string
      description: string | null
      image_url: string | null
      display_order: number | null
      is_active: boolean | null
      created_at: string | null
      updated_at: string | null
    }
    Insert: {
      id?: string
      store_id: string
      title: string
      description?: string | null
      image_url?: string | null
      display_order?: number | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
    }
    Update: {
      id?: string
      store_id?: string
      title?: string
      description?: string | null
      image_url?: string | null
      display_order?: number | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "store_community_showcase_store_id_fkey"
        columns: ["store_id"]
        isOneToOne: false
        referencedRelation: "stores"
        referencedColumns: ["id"]
      },
    ]
  }
  store_custom_quotes: {
    Row: {
      id: string
      store_id: string
      quote_text: string
      author: string | null
      display_order: number | null
      is_active: boolean | null
      created_at: string | null
      updated_at: string | null
    }
    Insert: {
      id?: string
      store_id: string
      quote_text: string
      author?: string | null
      display_order?: number | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
    }
    Update: {
      id?: string
      store_id?: string
      quote_text?: string
      author?: string | null
      display_order?: number | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "store_custom_quotes_store_id_fkey"
        columns: ["store_id"]
        isOneToOne: false
        referencedRelation: "stores"
        referencedColumns: ["id"]
      },
    ]
  }
  store_promotional_banners: {
    Row: {
      id: string
      store_id: string
      title: string
      description: string | null
      image_url: string | null
      link_url: string | null
      start_date: string | null
      end_date: string | null
      is_active: boolean | null
      created_at: string | null
      updated_at: string | null
    }
    Insert: {
      id?: string
      store_id: string
      title: string
      description?: string | null
      image_url?: string | null
      link_url?: string | null
      start_date?: string | null
      end_date?: string | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
    }
    Update: {
      id?: string
      store_id?: string
      title?: string
      description?: string | null
      image_url?: string | null
      link_url?: string | null
      start_date?: string | null
      end_date?: string | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "store_promotional_banners_store_id_fkey"
        columns: ["store_id"]
        isOneToOne: false
        referencedRelation: "stores"
        referencedColumns: ["id"]
      },
    ]
  }
  store_testimonials: {
    Row: {
      id: string
      store_id: string
      customer_name: string
      testimonial_text: string
      rating: number | null
      display_order: number | null
      is_active: boolean | null
      created_at: string | null
      updated_at: string | null
    }
    Insert: {
      id?: string
      store_id: string
      customer_name: string
      testimonial_text: string
      rating?: number | null
      display_order?: number | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
    }
    Update: {
      id?: string
      store_id?: string
      customer_name?: string
      testimonial_text?: string
      rating?: number | null
      display_order?: number | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "store_testimonials_store_id_fkey"
        columns: ["store_id"]
        isOneToOne: false
        referencedRelation: "stores"
        referencedColumns: ["id"]
      },
    ]
  }
}

export interface StoreFunctions {
  is_store_administrator: {
    Args: {
      user_id: string
      store_id: string
    }
    Returns: boolean
  }
}
