/**
 * MESSAGING Domain Types
 * 
 * Auto-generated from Supabase schema - DO NOT EDIT MANUALLY
 */

import { Json } from '../core/base';

export interface MessagingTables {
  conversations: {
    Row: {
      id: string
      created_at: string | null
      updated_at: string | null
      last_message_at: string | null
      is_group: boolean | null
      name: string | null
    }
    Insert: {
      id?: string
      created_at?: string | null
      updated_at?: string | null
      last_message_at?: string | null
      is_group?: boolean | null
      name?: string | null
    }
    Update: {
      id?: string
      created_at?: string | null
      updated_at?: string | null
      last_message_at?: string | null
      is_group?: boolean | null
      name?: string | null
    }
    Relationships: []
  }
  conversation_participants: {
    Row: {
      id: string
      conversation_id: string
      user_id: string
      joined_at: string | null
      left_at: string | null
      role: string | null
    }
    Insert: {
      id?: string
      conversation_id: string
      user_id: string
      joined_at?: string | null
      left_at?: string | null
      role?: string | null
    }
    Update: {
      id?: string
      conversation_id?: string
      user_id?: string
      joined_at?: string | null
      left_at?: string | null
      role?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "conversation_participants_conversation_id_fkey"
        columns: ["conversation_id"]
        isOneToOne: false
        referencedRelation: "conversations"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "conversation_participants_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  direct_messages: {
    Row: {
      id: string
      sender_id: string
      recipient_id: string
      content: string
      created_at: string | null
      read_at: string | null
      expires_at: string | null
      is_deleted: boolean | null
    }
    Insert: {
      id?: string
      sender_id: string
      recipient_id: string
      content: string
      created_at?: string | null
      read_at?: string | null
      expires_at?: string | null
      is_deleted?: boolean | null
    }
    Update: {
      id?: string
      sender_id?: string
      recipient_id?: string
      content?: string
      created_at?: string | null
      read_at?: string | null
      expires_at?: string | null
      is_deleted?: boolean | null
    }
    Relationships: [
      {
        foreignKeyName: "direct_messages_sender_id_fkey"
        columns: ["sender_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "direct_messages_recipient_id_fkey"
        columns: ["recipient_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  chat_messages: {
    Row: {
      id: string
      conversation_id: string
      sender_id: string
      content: string
      created_at: string | null
      updated_at: string | null
      message_type: string | null
      reply_to_id: string | null
    }
    Insert: {
      id?: string
      conversation_id: string
      sender_id: string
      content: string
      created_at?: string | null
      updated_at?: string | null
      message_type?: string | null
      reply_to_id?: string | null
    }
    Update: {
      id?: string
      conversation_id?: string
      sender_id?: string
      content?: string
      created_at?: string | null
      updated_at?: string | null
      message_type?: string | null
      reply_to_id?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "chat_messages_conversation_id_fkey"
        columns: ["conversation_id"]
        isOneToOne: false
        referencedRelation: "conversations"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "chat_messages_sender_id_fkey"
        columns: ["sender_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  message_reactions: {
    Row: {
      id: string
      message_id: string
      user_id: string
      emoji: string
      created_at: string | null
    }
    Insert: {
      id?: string
      message_id: string
      user_id: string
      emoji: string
      created_at?: string | null
    }
    Update: {
      id?: string
      message_id?: string
      user_id?: string
      emoji?: string
      created_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "message_reactions_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  private_chats: {
    Row: {
      id: string
      participant_1: string
      participant_2: string
      created_at: string | null
      last_message_at: string | null
    }
    Insert: {
      id?: string
      participant_1: string
      participant_2: string
      created_at?: string | null
      last_message_at?: string | null
    }
    Update: {
      id?: string
      participant_1?: string
      participant_2?: string
      created_at?: string | null
      last_message_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "private_chats_participant_1_fkey"
        columns: ["participant_1"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "private_chats_participant_2_fkey"
        columns: ["participant_2"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
}

export interface MessagingFunctions {
  cleanup_expired_messages: {
    Args: Record<PropertyKey, never>
    Returns: undefined
  }
  get_unread_message_count: {
    Args: {
      user_id: string
    }
    Returns: number
  }
  soft_delete_expired_messages: {
    Args: Record<PropertyKey, never>
    Returns: undefined
  }
  test_messaging_rls_policies: {
    Args: Record<PropertyKey, never>
    Returns: Json
  }
}
