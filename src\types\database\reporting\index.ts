/**
 * REPORTING Domain Types
 * 
 * Auto-generated from Supabase schema - DO NOT EDIT MANUALLY
 */

import { Json } from '../core/base';

export interface ReportingTables {
  reports: {
    Row: {
      id: string
      reporter_id: string
      reported_user_id: string | null
      reported_content_id: string | null
      content_type: string | null
      reason: string
      description: string | null
      status: string | null
      created_at: string | null
      updated_at: string | null
      resolved_at: string | null
      resolved_by: string | null
      resolution_notes: string | null
    }
    Insert: {
      id?: string
      reporter_id: string
      reported_user_id?: string | null
      reported_content_id?: string | null
      content_type?: string | null
      reason: string
      description?: string | null
      status?: string | null
      created_at?: string | null
      updated_at?: string | null
      resolved_at?: string | null
      resolved_by?: string | null
      resolution_notes?: string | null
    }
    Update: {
      id?: string
      reporter_id?: string
      reported_user_id?: string | null
      reported_content_id?: string | null
      content_type?: string | null
      reason?: string
      description?: string | null
      status?: string | null
      created_at?: string | null
      updated_at?: string | null
      resolved_at?: string | null
      resolved_by?: string | null
      resolution_notes?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "reports_reporter_id_fkey"
        columns: ["reporter_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "reports_reported_user_id_fkey"
        columns: ["reported_user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "reports_resolved_by_fkey"
        columns: ["resolved_by"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  report_evidence: {
    Row: {
      id: string
      report_id: string
      evidence_type: string
      evidence_url: string | null
      evidence_text: string | null
      uploaded_at: string | null
    }
    Insert: {
      id?: string
      report_id: string
      evidence_type: string
      evidence_url?: string | null
      evidence_text?: string | null
      uploaded_at?: string | null
    }
    Update: {
      id?: string
      report_id?: string
      evidence_type?: string
      evidence_url?: string | null
      evidence_text?: string | null
      uploaded_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "report_evidence_report_id_fkey"
        columns: ["report_id"]
        isOneToOne: false
        referencedRelation: "reports"
        referencedColumns: ["id"]
      },
    ]
  }
}

export interface ReportingFunctions {
  // No reporting-specific functions currently defined
}
