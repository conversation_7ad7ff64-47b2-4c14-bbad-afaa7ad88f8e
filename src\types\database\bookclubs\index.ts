/**
 * BOOKCLUBS Domain Types
 * 
 * Auto-generated from Supabase schema - DO NOT EDIT MANUALLY
 */

import { Json } from '../core/base';

export interface BookclubsTables {
  book_clubs: {
    Row: {
      access_tier_required: string | null
      created_at: string | null
      description: string | null
      id: string
      is_exclusive: boolean | null
      is_premium: boolean | null
      join_questions_enabled: boolean | null
      lead_user_id: string
      name: string
      privacy: string | null
      progress_tracking_enabled: boolean | null
      store_id: string | null
      updated_at: string | null
    }
    Insert: {
      access_tier_required?: string | null
      created_at?: string | null
      description?: string | null
      id?: string
      is_exclusive?: boolean | null
      is_premium?: boolean | null
      join_questions_enabled?: boolean | null
      lead_user_id: string
      name: string
      privacy?: string | null
      progress_tracking_enabled?: boolean | null
      store_id?: string | null
      updated_at?: string | null
    }
    Update: {
      access_tier_required?: string | null
      created_at?: string | null
      description?: string | null
      id?: string
      is_exclusive?: boolean | null
      is_premium?: boolean | null
      join_questions_enabled?: boolean | null
      lead_user_id?: string
      name?: string
      privacy?: string | null
      progress_tracking_enabled?: boolean | null
      store_id?: string | null
      updated_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "book_clubs_store_id_fkey"
        columns: ["store_id"]
        isOneToOne: false
        referencedRelation: "stores"
        referencedColumns: ["id"]
      },
    ]
  }
  book_likes: {
    Row: {
      created_at: string | null
      nomination_id: string
      user_id: string
    }
    Insert: {
      created_at?: string | null
      nomination_id: string
      user_id: string
    }
    Update: {
      created_at?: string | null
      nomination_id?: string
      user_id?: string
    }
    Relationships: [
      {
        foreignKeyName: "book_likes_nomination_id_fkey"
        columns: ["nomination_id"]
        isOneToOne: false
        referencedRelation: "book_nominations"
        referencedColumns: ["id"]
      },
    ]
  }
  book_nominations: {
    Row: {
      book_id: string | null
      club_id: string | null
      id: string
      nominated_at: string | null
      nominated_by: string | null
      status: string | null
    }
    Insert: {
      book_id?: string | null
      club_id?: string | null
      id?: string
      nominated_at?: string | null
      nominated_by?: string | null
      status?: string | null
    }
    Update: {
      book_id?: string | null
      club_id?: string | null
      id?: string
      nominated_at?: string | null
      nominated_by?: string | null
      status?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "book_nominations_book_id_fkey"
        columns: ["book_id"]
        isOneToOne: false
        referencedRelation: "books"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "book_nominations_club_id_fkey"
        columns: ["club_id"]
        isOneToOne: false
        referencedRelation: "book_clubs"
        referencedColumns: ["id"]
      },
    ]
  }
  books: {
    Row: {
      author: string
      cover_url: string | null
      created_at: string | null
      description: string | null
      genre: string | null
      google_books_id: string | null
      id: string
      title: string
    }
    Insert: {
      author: string
      cover_url?: string | null
      created_at?: string | null
      description?: string | null
      genre?: string | null
      google_books_id?: string | null
      id?: string
      title: string
    }
    Update: {
      author?: string
      cover_url?: string | null
      created_at?: string | null
      description?: string | null
      genre?: string | null
      google_books_id?: string | null
      id?: string
      title?: string
    }
    Relationships: []
  }
  book_reviews: {
    Row: {
      id: string
      user_id: string
      book_id: string
      reading_list_item_id: string | null
      rating: number | null
      review_text: string | null
      is_private: boolean
      created_at: string
      updated_at: string
    }
    Insert: {
      id?: string
      user_id: string
      book_id: string
      reading_list_item_id?: string | null
      rating?: number | null
      review_text?: string | null
      is_private?: boolean
      created_at?: string
      updated_at?: string
    }
    Update: {
      id?: string
      user_id?: string
      book_id?: string
      reading_list_item_id?: string | null
      rating?: number | null
      review_text?: string | null
      is_private?: boolean
      created_at?: string
      updated_at?: string
    }
    Relationships: [
      {
        foreignKeyName: "book_reviews_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "book_reviews_book_id_fkey"
        columns: ["book_id"]
        isOneToOne: false
        referencedRelation: "books"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "book_reviews_reading_list_item_id_fkey"
        columns: ["reading_list_item_id"]
        isOneToOne: false
        referencedRelation: "reading_list_items"
        referencedColumns: ["id"]
      },
    ]
  }
  club_analytics_snapshots: {
    Row: {
      active_members_week: number | null
      club_id: string
      created_at: string | null
      current_book_progress: number | null
      discussion_count: number | null
      id: string
      meeting_attendance_rate: number | null
      meetings_this_month: number | null
      member_count: number | null
      new_members_month: number | null
      posts_count: number | null
      posts_this_week: number | null
      reading_completion_rate: number | null
      snapshot_date: string
      updated_at: string | null
    }
    Insert: {
      active_members_week?: number | null
      club_id: string
      created_at?: string | null
      current_book_progress?: number | null
      discussion_count?: number | null
      id?: string
      meeting_attendance_rate?: number | null
      meetings_this_month?: number | null
      member_count?: number | null
      new_members_month?: number | null
      posts_count?: number | null
      posts_this_week?: number | null
      reading_completion_rate?: number | null
      snapshot_date: string
      updated_at?: string | null
    }
    Update: {
      active_members_week?: number | null
      club_id?: string
      created_at?: string | null
      current_book_progress?: number | null
      discussion_count?: number | null
      id?: string
      meeting_attendance_rate?: number | null
      meetings_this_month?: number | null
      member_count?: number | null
      new_members_month?: number | null
      posts_count?: number | null
      posts_this_week?: number | null
      reading_completion_rate?: number | null
      snapshot_date?: string
      updated_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "club_analytics_snapshots_club_id_fkey"
        columns: ["club_id"]
        isOneToOne: false
        referencedRelation: "book_clubs"
        referencedColumns: ["id"]
      },
    ]
  }
}
