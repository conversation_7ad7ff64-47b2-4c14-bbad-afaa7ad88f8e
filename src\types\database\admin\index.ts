/**
 * ADMIN Domain Types
 * 
 * Auto-generated from Supabase schema - DO NOT EDIT MANUALLY
 */

import { Json } from '../core/base';

export interface AdminTables {
  events: {
    Row: {
      id: string
      title: string
      description: string | null
      event_date: string
      location: string | null
      max_participants: number | null
      registration_deadline: string | null
      is_active: boolean | null
      created_at: string | null
      updated_at: string | null
      created_by: string | null
    }
    Insert: {
      id?: string
      title: string
      description?: string | null
      event_date: string
      location?: string | null
      max_participants?: number | null
      registration_deadline?: string | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
      created_by?: string | null
    }
    Update: {
      id?: string
      title?: string
      description?: string | null
      event_date?: string
      location?: string | null
      max_participants?: number | null
      registration_deadline?: string | null
      is_active?: boolean | null
      created_at?: string | null
      updated_at?: string | null
      created_by?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "events_created_by_fkey"
        columns: ["created_by"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  event_participants: {
    Row: {
      id: string
      event_id: string
      user_id: string
      registration_date: string | null
      attendance_status: string | null
      notes: string | null
    }
    Insert: {
      id?: string
      event_id: string
      user_id: string
      registration_date?: string | null
      attendance_status?: string | null
      notes?: string | null
    }
    Update: {
      id?: string
      event_id?: string
      user_id?: string
      registration_date?: string | null
      attendance_status?: string | null
      notes?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "event_participants_event_id_fkey"
        columns: ["event_id"]
        isOneToOne: false
        referencedRelation: "events"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "event_participants_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  event_notifications: {
    Row: {
      id: string
      event_id: string
      user_id: string
      notification_type: string
      sent_at: string | null
      read_at: string | null
    }
    Insert: {
      id?: string
      event_id: string
      user_id: string
      notification_type: string
      sent_at?: string | null
      read_at?: string | null
    }
    Update: {
      id?: string
      event_id?: string
      user_id?: string
      notification_type?: string
      sent_at?: string | null
      read_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "event_notifications_event_id_fkey"
        columns: ["event_id"]
        isOneToOne: false
        referencedRelation: "events"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "event_notifications_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
}

export interface AdminFunctions {
  // No admin-specific functions currently defined
}
