/**
 * CORE Domain Types
 * 
 * Auto-generated from Supabase schema - DO NOT EDIT MANUALLY
 */

import { Json } from './base';

export interface CoreTables {
  discussion_topics: {
    Row: {
      id: string
      title: string
      description: string | null
      created_by: string
      created_at: string | null
      updated_at: string | null
      is_pinned: boolean | null
      is_locked: boolean | null
    }
    Insert: {
      id?: string
      title: string
      description?: string | null
      created_by: string
      created_at?: string | null
      updated_at?: string | null
      is_pinned?: boolean | null
      is_locked?: boolean | null
    }
    Update: {
      id?: string
      title?: string
      description?: string | null
      created_by?: string
      created_at?: string | null
      updated_at?: string | null
      is_pinned?: boolean | null
      is_locked?: boolean | null
    }
    Relationships: [
      {
        foreignKeyName: "discussion_topics_created_by_fkey"
        columns: ["created_by"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  discussion_posts: {
    Row: {
      id: string
      topic_id: string
      author_id: string
      content: string
      created_at: string | null
      updated_at: string | null
      is_deleted: boolean | null
      parent_post_id: string | null
    }
    Insert: {
      id?: string
      topic_id: string
      author_id: string
      content: string
      created_at?: string | null
      updated_at?: string | null
      is_deleted?: boolean | null
      parent_post_id?: string | null
    }
    Update: {
      id?: string
      topic_id?: string
      author_id?: string
      content?: string
      created_at?: string | null
      updated_at?: string | null
      is_deleted?: boolean | null
      parent_post_id?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "discussion_posts_topic_id_fkey"
        columns: ["topic_id"]
        isOneToOne: false
        referencedRelation: "discussion_topics"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "discussion_posts_author_id_fkey"
        columns: ["author_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  post_reactions: {
    Row: {
      id: string
      post_id: string
      user_id: string
      reaction_type: string
      created_at: string | null
    }
    Insert: {
      id?: string
      post_id: string
      user_id: string
      reaction_type: string
      created_at?: string | null
    }
    Update: {
      id?: string
      post_id?: string
      user_id?: string
      reaction_type?: string
      created_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "post_reactions_post_id_fkey"
        columns: ["post_id"]
        isOneToOne: false
        referencedRelation: "discussion_posts"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "post_reactions_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  platform_settings: {
    Row: {
      id: string
      setting_key: string
      setting_value: string | null
      description: string | null
      updated_at: string | null
      updated_by: string | null
    }
    Insert: {
      id?: string
      setting_key: string
      setting_value?: string | null
      description?: string | null
      updated_at?: string | null
      updated_by?: string | null
    }
    Update: {
      id?: string
      setting_key?: string
      setting_value?: string | null
      description?: string | null
      updated_at?: string | null
      updated_by?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "platform_settings_updated_by_fkey"
        columns: ["updated_by"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  moderation_actions: {
    Row: {
      id: string
      moderator_id: string
      target_user_id: string | null
      target_content_id: string | null
      action_type: string
      reason: string | null
      created_at: string | null
    }
    Insert: {
      id?: string
      moderator_id: string
      target_user_id?: string | null
      target_content_id?: string | null
      action_type: string
      reason?: string | null
      created_at?: string | null
    }
    Update: {
      id?: string
      moderator_id?: string
      target_user_id?: string | null
      target_content_id?: string | null
      action_type?: string
      reason?: string | null
      created_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "moderation_actions_moderator_id_fkey"
        columns: ["moderator_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "moderation_actions_target_user_id_fkey"
        columns: ["target_user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
  role_activity: {
    Row: {
      id: string
      user_id: string
      role: string
      activity_type: string
      activity_data: Json | null
      created_at: string | null
    }
    Insert: {
      id?: string
      user_id: string
      role: string
      activity_type: string
      activity_data?: Json | null
      created_at?: string | null
    }
    Update: {
      id?: string
      user_id?: string
      role?: string
      activity_type?: string
      activity_data?: Json | null
      created_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "role_activity_user_id_fkey"
        columns: ["user_id"]
        isOneToOne: false
        referencedRelation: "users"
        referencedColumns: ["id"]
      },
    ]
  }
}

export interface CoreFunctions {
  can_moderate_content: {
    Args: {
      user_id: string
      content_type: string
    }
    Returns: boolean
  }
  is_platform_owner: {
    Args: {
      user_id: string
    }
    Returns: boolean
  }
  update_role_activity: {
    Args: {
      user_id: string
      role: string
      activity_type: string
      activity_data: Json
    }
    Returns: undefined
  }
}
