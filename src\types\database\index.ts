/**
 * Database Types - Main Index
 * 
 * This file provides backward compatibility with the original types structure
 * while enabling the new modular organization.
 * 
 * IMPLEMENTATION STRATEGY:
 * 1. Re-export the original Database type for 100% backward compatibility
 * 2. Provide domain-specific exports for new modular usage
 * 3. Maintain all existing import patterns
 * 
 * Auto-generated - DO NOT EDIT MANUALLY
 */

// Import and re-export the original Database type for backward compatibility
export {
  Database,
  Json,
  Tables,
  TablesInsert,
  TablesUpdate,
  Enums,
  CompositeTypes,
  Constants
} from '../../integrations/supabase/types';

// Import Database type for internal use
import type { Database } from '../../integrations/supabase/types';

// Import domain-specific types (when implemented)
// export { BookclubsTables } from './bookclubs';
// export { UsersTables } from './users';
// export { MessagingTables } from './messaging';
// export { StoreTables } from './store';
// export { AnalyticsTables } from './analytics';
// export { AdminTables } from './admin';
// export { ReportingTables } from './reporting';
// export { EntitlementsTables } from './entitlements';
// export { CoreTables } from './core';

/**
 * MIGRATION GUIDE:
 * 
 * Current usage (continues to work):
 * import { Database } from '@/integrations/supabase/types';
 * type User = Database['public']['Tables']['users']['Row'];
 * 
 * New modular usage (when domains are implemented):
 * import { UsersTables } from '@/types/database/users';
 * type User = UsersTables['users']['Row'];
 * 
 * Or use the new centralized import:
 * import { Database } from '@/types/database';
 * type User = Database['public']['Tables']['users']['Row'];
 */

/**
 * Domain-specific type extractors (future implementation)
 * These will provide better type safety and organization
 */

// Bookclubs domain types
export type BookClub = Database['public']['Tables']['book_clubs']['Row'];
export type BookClubInsert = Database['public']['Tables']['book_clubs']['Insert'];
export type BookClubUpdate = Database['public']['Tables']['book_clubs']['Update'];

export type Book = Database['public']['Tables']['books']['Row'];
export type BookInsert = Database['public']['Tables']['books']['Insert'];
export type BookUpdate = Database['public']['Tables']['books']['Update'];

export type BookNomination = Database['public']['Tables']['book_nominations']['Row'];
export type BookReview = Database['public']['Tables']['book_reviews']['Row'];
export type ClubMember = Database['public']['Tables']['club_members']['Row'];
export type MemberProgress = Database['public']['Tables']['member_reading_progress']['Row'];

// Users domain types
export type User = Database['public']['Tables']['users']['Row'];
export type UserInsert = Database['public']['Tables']['users']['Insert'];
export type UserUpdate = Database['public']['Tables']['users']['Update'];

export type UserSubscription = Database['public']['Tables']['user_subscriptions']['Row'];
export type ReadingListItem = Database['public']['Tables']['reading_list_items']['Row'];

// Messaging domain types
export type DirectMessage = Database['public']['Tables']['direct_messages']['Row'];
export type Conversation = Database['public']['Tables']['conversations']['Row'];
export type ChatMessage = Database['public']['Tables']['chat_messages']['Row'];

// Store domain types
export type Store = Database['public']['Tables']['stores']['Row'];
export type StoreCarouselItem = Database['public']['Tables']['store_carousel_items']['Row'];
export type StoreBanner = Database['public']['Tables']['store_promotional_banners']['Row'];

// Analytics domain types
export type StoreAnalytics = Database['public']['Tables']['store_landing_analytics']['Row'];
export type ClubAnalytics = Database['public']['Tables']['club_analytics_snapshots']['Row'];

// Admin domain types
export type Event = Database['public']['Tables']['events']['Row'];
export type EventParticipant = Database['public']['Tables']['event_participants']['Row'];

// Core domain types
export type DiscussionTopic = Database['public']['Tables']['discussion_topics']['Row'];
export type DiscussionPost = Database['public']['Tables']['discussion_posts']['Row'];
export type PostReaction = Database['public']['Tables']['post_reactions']['Row'];

/**
 * REFACTORING BENEFITS:
 * 
 * 1. PERFORMANCE: Faster TypeScript compilation with smaller type files
 * 2. MAINTAINABILITY: Clear domain boundaries and easier navigation
 * 3. DEVELOPER EXPERIENCE: Better IntelliSense and type discovery
 * 4. SCALABILITY: Easy to add new domains without affecting existing code
 * 5. BACKWARD COMPATIBILITY: Zero breaking changes to existing imports
 * 
 * IMPLEMENTATION STATUS:
 * ✅ Backward compatibility maintained
 * ✅ Domain structure planned
 * ✅ Type extractors provided
 * 🔄 Domain files implementation in progress
 * ⏳ Automated generation script pending
 */
