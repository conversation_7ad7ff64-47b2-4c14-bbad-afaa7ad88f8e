#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SrcAnalyzer {
  constructor() {
    this.stats = {
      totalLines: 0,
      totalFiles: 0,
      bySubdirectory: {},
      componentBreakdown: {},
      fileDetails: []
    };
  }

  countLinesInFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      return content.split('\n').length;
    } catch (error) {
      return 0;
    }
  }

  isSourceFile(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return ['.ts', '.tsx', '.js', '.jsx', '.css', '.scss'].includes(ext);
  }

  analyzeDirectory(dirPath, relativePath = '') {
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const relativeItemPath = path.join(relativePath, item);
        
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          this.analyzeDirectory(fullPath, relativeItemPath);
        } else if (stat.isFile() && this.isSourceFile(fullPath)) {
          this.analyzeFile(fullPath, relativeItemPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not read directory ${dirPath}`);
    }
  }

  analyzeFile(filePath, relativePath) {
    const lines = this.countLinesInFile(filePath);
    const ext = path.extname(filePath);
    const directory = path.dirname(relativePath) || 'root';
    const topLevelDir = relativePath.split(path.sep)[0] || 'root';
    
    // Update totals
    this.stats.totalLines += lines;
    this.stats.totalFiles += 1;
    
    // Update by subdirectory
    if (!this.stats.bySubdirectory[topLevelDir]) {
      this.stats.bySubdirectory[topLevelDir] = { files: 0, lines: 0, types: {} };
    }
    this.stats.bySubdirectory[topLevelDir].files += 1;
    this.stats.bySubdirectory[topLevelDir].lines += lines;
    
    // Track file types within directories
    if (!this.stats.bySubdirectory[topLevelDir].types[ext]) {
      this.stats.bySubdirectory[topLevelDir].types[ext] = { files: 0, lines: 0 };
    }
    this.stats.bySubdirectory[topLevelDir].types[ext].files += 1;
    this.stats.bySubdirectory[topLevelDir].types[ext].lines += lines;
    
    // Component analysis
    if (topLevelDir === 'components') {
      const componentPath = relativePath.split(path.sep).slice(0, 3).join('/');
      if (!this.stats.componentBreakdown[componentPath]) {
        this.stats.componentBreakdown[componentPath] = { files: 0, lines: 0 };
      }
      this.stats.componentBreakdown[componentPath].files += 1;
      this.stats.componentBreakdown[componentPath].lines += lines;
    }
    
    // Store file details
    this.stats.fileDetails.push({
      path: relativePath,
      lines: lines,
      ext: ext,
      directory: topLevelDir
    });
  }

  generateReport() {
    console.log('📊 BookTalks Buddy - Source Code Analysis');
    console.log('==========================================\n');
    
    // Overall src stats
    console.log('📈 SRC DIRECTORY STATISTICS');
    console.log(`Total Source Lines: ${this.stats.totalLines.toLocaleString()}`);
    console.log(`Total Source Files: ${this.stats.totalFiles.toLocaleString()}\n`);
    
    // By subdirectory
    console.log('📁 BREAKDOWN BY SRC SUBDIRECTORIES');
    console.log('-----------------------------------');
    const sortedByDir = Object.entries(this.stats.bySubdirectory)
      .sort((a, b) => b[1].lines - a[1].lines);
    
    for (const [dir, data] of sortedByDir) {
      const percentage = ((data.lines / this.stats.totalLines) * 100).toFixed(1);
      console.log(`${dir.padEnd(25)} ${data.lines.toLocaleString().padStart(8)} lines (${percentage}%) in ${data.files} files`);
      
      // Show file type breakdown for this directory
      const sortedTypes = Object.entries(data.types).sort((a, b) => b[1].lines - a[1].lines);
      for (const [ext, typeData] of sortedTypes) {
        console.log(`  ${ext.padEnd(23)} ${typeData.lines.toLocaleString().padStart(8)} lines in ${typeData.files} files`);
      }
      console.log();
    }
    
    // Component breakdown
    if (Object.keys(this.stats.componentBreakdown).length > 0) {
      console.log('🧩 COMPONENT BREAKDOWN (Top 15)');
      console.log('--------------------------------');
      const sortedComponents = Object.entries(this.stats.componentBreakdown)
        .sort((a, b) => b[1].lines - a[1].lines)
        .slice(0, 15);
      
      for (const [component, data] of sortedComponents) {
        console.log(`${component.padEnd(40)} ${data.lines.toLocaleString().padStart(6)} lines in ${data.files} files`);
      }
      console.log();
    }
    
    // Largest source files
    console.log('📄 LARGEST SOURCE FILES (Top 15)');
    console.log('----------------------------------');
    const largestFiles = this.stats.fileDetails
      .sort((a, b) => b.lines - a.lines)
      .slice(0, 15);
    
    for (const file of largestFiles) {
      console.log(`${file.path.padEnd(60)} ${file.lines.toString().padStart(6)} lines [${file.ext}]`);
    }
    console.log();
    
    // File type summary
    console.log('📋 FILE TYPE SUMMARY');
    console.log('--------------------');
    const typeStats = {};
    
    for (const file of this.stats.fileDetails) {
      if (!typeStats[file.ext]) {
        typeStats[file.ext] = { files: 0, lines: 0 };
      }
      typeStats[file.ext].files += 1;
      typeStats[file.ext].lines += file.lines;
    }
    
    const sortedTypes = Object.entries(typeStats).sort((a, b) => b[1].lines - a[1].lines);
    for (const [ext, data] of sortedTypes) {
      const percentage = ((data.lines / this.stats.totalLines) * 100).toFixed(1);
      console.log(`${ext.padEnd(10)} ${data.lines.toLocaleString().padStart(8)} lines (${percentage}%) in ${data.files} files`);
    }
  }

  run() {
    console.log('🔍 Analyzing src directory...\n');
    const srcPath = path.join(__dirname, 'src');
    this.analyzeDirectory(srcPath);
    this.generateReport();
  }
}

// Run the analysis
const analyzer = new SrcAnalyzer();
analyzer.run();
