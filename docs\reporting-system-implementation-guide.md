# BookTalks Buddy Reporting System Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing and testing the basic reporting system in BookTalks Buddy. The system allows users to report inappropriate content and provides moderation tools for club leads, moderators, and store owners.

## Architecture Summary

- **Frontend**: Vite + React with direct Supabase client calls
- **Backend**: Supabase with Row Level Security (RLS)
- **Database**: PostgreSQL with comprehensive reporting schema
- **Authentication**: Supabase Auth with entitlements system

## Implementation Status

### ✅ Completed Components

1. **Database Schema** - All reporting tables created via migration
2. **TypeScript Types** - Complete type definitions in `src/types/reporting.ts`
3. **Backend Services** - Core functions in `src/services/reportingService.ts`
4. **UI Components** - ReportButton, ReportDialog, ModerationDashboard
5. **Role-Based Access** - Enhanced filtering for club leads and moderators
6. **Store Escalation** - Direct escalation mechanism to store owners

### 🔧 Key Features Implemented

- **Report Creation**: Users can report any content type
- **Role-Based Visibility**: Reports visible to both club leads AND moderators
- **Store Escalation**: Manual escalation button for unresolved reports
- **Severity Calculation**: Automatic severity assignment based on reason
- **Audit Trail**: Complete history of all actions and escalations

## Testing the Implementation

### Step 1: Verify Database Migration

1. Open Supabase SQL Editor
2. Run the verification query:
```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
  'reports', 'report_evidence', 'moderation_actions', 
  'user_warnings', 'workflow_triggers', 'escalation_rules',
  'workflow_states', 'escalation_history'
);
```

Expected result: All 8 tables should be listed.

### Step 2: Access Test Page

1. Log in as any user
2. Navigate to `/admin/reporting-test`
3. Click "Verify Database" to run automated checks
4. All checks should pass with green status

### Step 3: Test Report Creation

1. On the test page, click "Create Test Report"
2. Alternatively, use the ReportButton component
3. Fill out the report form and submit
4. Verify the report appears in the reports list

### Step 4: Test Role-Based Access

1. **As Club Lead**: Create a club and verify you can see reports for that club
2. **As Moderator**: Get appointed as moderator and verify you can see the same reports
3. **As Store Owner**: Verify you can see all reports in your store

### Step 5: Test Escalation

1. Create a report in a club
2. As club lead/moderator, click "Escalate to Store" button
3. Verify report status changes to "escalated"
4. Check escalation_history table for audit trail

## User Roles and Permissions

### Club Lead
- **Sees**: All reports for their clubs
- **Can**: Resolve reports, escalate to store owner
- **Determined by**: `book_clubs.lead_user_id` field

### Club Moderator  
- **Sees**: All reports for clubs they moderate
- **Can**: Resolve reports, escalate to store owner
- **Determined by**: `club_moderators` table

### Store Owner
- **Sees**: All reports for their store
- **Can**: Resolve any report, final authority
- **Determined by**: Store ownership entitlements

## API Functions Available

### Core Functions
```typescript
// Create a new report
createReport(userId: string, reportData: CreateReportData, context?: ReportContext)

// Get reports with role-based filtering
getReports(filters: ReportFilters, pagination: PaginationOptions, userContext?: UserContext)

// Update report status
updateReport(reportId: string, updates: UpdateReportData, moderatorUserId: string)

// Escalate to store owner
escalateToStoreOwner(reportId: string, escalatorUserId: string, reason: string)

// Get statistics
getReportStats(filters: ReportFilters)
```

### Integration Points

Reports are integrated into:
- Discussion posts (`src/components/discussions/TopicDetail.tsx`)
- User profiles (`src/pages/UserProfile.tsx`)
- Events (`src/pages/EventDetailsPage.tsx`)
- Book clubs (`src/components/bookclubs/sections/ClubHeader.tsx`)

## Escalation Workflow

### Manual Escalation
1. Club lead/moderator clicks "Escalate to Store" button
2. Report status changes to "escalated"
3. Store owner receives report in their dashboard
4. Escalation history is recorded for audit

### Automatic Escalation (Future)
- Time-based escalation rules are defined in database
- Workflow triggers table supports automation
- Currently requires manual implementation

## Troubleshooting

### Common Issues

1. **Reports not visible**: Check user entitlements and club membership
2. **Escalation fails**: Verify store_id is properly set on clubs
3. **Database errors**: Run verification script to check table existence

### Debug Tools

- **Test Page**: `/admin/reporting-test` for comprehensive testing
- **Verification Script**: `src/utils/verifyReportingDatabase.ts`
- **Console Logging**: Detailed error messages in browser console

## Security Considerations

- **RLS Policies**: All data access controlled by Supabase RLS
- **Role Validation**: Entitlements system validates all permissions
- **Audit Trail**: Complete history of all actions and changes
- **Self-Report Prevention**: Users cannot report their own content

## Next Steps

### Phase 2 Enhancements (Optional)
1. **Notification System**: Alert moderators of new reports
2. **Batch Processing**: Handle multiple reports simultaneously  
3. **Advanced Filtering**: More sophisticated search and filter options
4. **Appeal System**: Allow users to appeal moderation decisions
5. **Automated Escalation**: Time-based escalation triggers

### Performance Optimizations
1. **Caching**: Cache user entitlements and role data
2. **Indexing**: Add database indexes for common query patterns
3. **Pagination**: Implement proper pagination for large report lists

## Support

For questions or issues:
1. Check the test page for verification results
2. Review console logs for detailed error messages
3. Verify database migration status in Supabase dashboard
4. Test with different user roles to isolate permission issues

The basic reporting system is now functional and ready for production use with the core features implemented.
