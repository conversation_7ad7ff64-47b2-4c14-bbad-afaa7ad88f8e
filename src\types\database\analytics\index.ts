/**
 * ANALYTICS Domain Types
 * 
 * Auto-generated from Supabase schema - DO NOT EDIT MANUALLY
 */

import { Json } from '../core/base';

export interface AnalyticsTables {
  store_landing_analytics: {
    Row: {
      id: string
      store_id: string
      page_views: number | null
      unique_visitors: number | null
      bounce_rate: number | null
      avg_session_duration: number | null
      conversion_rate: number | null
      date: string
      created_at: string | null
    }
    Insert: {
      id?: string
      store_id: string
      page_views?: number | null
      unique_visitors?: number | null
      bounce_rate?: number | null
      avg_session_duration?: number | null
      conversion_rate?: number | null
      date: string
      created_at?: string | null
    }
    Update: {
      id?: string
      store_id?: string
      page_views?: number | null
      unique_visitors?: number | null
      bounce_rate?: number | null
      avg_session_duration?: number | null
      conversion_rate?: number | null
      date?: string
      created_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "store_landing_analytics_store_id_fkey"
        columns: ["store_id"]
        isOneToOne: false
        referencedRelation: "stores"
        referencedColumns: ["id"]
      },
    ]
  }
  store_landing_customization: {
    Row: {
      id: string
      store_id: string
      hero_title: string | null
      hero_subtitle: string | null
      hero_image_url: string | null
      primary_color: string | null
      secondary_color: string | null
      font_family: string | null
      custom_css: string | null
      created_at: string | null
      updated_at: string | null
    }
    Insert: {
      id?: string
      store_id: string
      hero_title?: string | null
      hero_subtitle?: string | null
      hero_image_url?: string | null
      primary_color?: string | null
      secondary_color?: string | null
      font_family?: string | null
      custom_css?: string | null
      created_at?: string | null
      updated_at?: string | null
    }
    Update: {
      id?: string
      store_id?: string
      hero_title?: string | null
      hero_subtitle?: string | null
      hero_image_url?: string | null
      primary_color?: string | null
      secondary_color?: string | null
      font_family?: string | null
      custom_css?: string | null
      created_at?: string | null
      updated_at?: string | null
    }
    Relationships: [
      {
        foreignKeyName: "store_landing_customization_store_id_fkey"
        columns: ["store_id"]
        isOneToOne: false
        referencedRelation: "stores"
        referencedColumns: ["id"]
      },
    ]
  }
}

export interface AnalyticsFunctions {
  create_daily_analytics_snapshot: {
    Args: Record<PropertyKey, never>
    Returns: undefined
  }
}
