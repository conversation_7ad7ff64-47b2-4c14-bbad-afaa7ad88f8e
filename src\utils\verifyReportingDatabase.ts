/**
 * Database Verification Utility for Reporting System
 * 
 * This utility checks if all required reporting tables exist and are properly configured.
 * Run this to verify the database migration was successful.
 */

import { supabase } from '@/lib/supabase';

interface TableInfo {
  table_name: string;
  exists: boolean;
  columns?: string[];
  error?: string;
}

interface VerificationResult {
  success: boolean;
  tables: TableInfo[];
  summary: {
    total_tables: number;
    existing_tables: number;
    missing_tables: number;
  };
  errors: string[];
}

const REQUIRED_TABLES = [
  'reports',
  'report_evidence', 
  'moderation_actions',
  'user_warnings',
  'workflow_triggers',
  'escalation_rules',
  'workflow_states',
  'escalation_history'
];

/**
 * Verify that all reporting system tables exist in the database
 */
export async function verifyReportingDatabase(): Promise<VerificationResult> {
  const result: VerificationResult = {
    success: false,
    tables: [],
    summary: {
      total_tables: REQUIRED_TABLES.length,
      existing_tables: 0,
      missing_tables: 0
    },
    errors: []
  };

  try {
    console.log('🔍 Verifying reporting system database tables...');

    for (const tableName of REQUIRED_TABLES) {
      try {
        // Check if table exists by querying information_schema
        const { data: tableExists, error: tableError } = await supabase
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_schema', 'public')
          .eq('table_name', tableName)
          .single();

        if (tableError && !tableError.message.includes('No rows')) {
          // Try alternative method - direct query to table
          const { error: directError } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);

          if (directError) {
            result.tables.push({
              table_name: tableName,
              exists: false,
              error: directError.message
            });
            result.summary.missing_tables++;
            result.errors.push(`Table '${tableName}' does not exist: ${directError.message}`);
          } else {
            // Table exists, get column info
            const columns = await getTableColumns(tableName);
            result.tables.push({
              table_name: tableName,
              exists: true,
              columns
            });
            result.summary.existing_tables++;
          }
        } else if (tableExists) {
          // Table exists, get column info
          const columns = await getTableColumns(tableName);
          result.tables.push({
            table_name: tableName,
            exists: true,
            columns
          });
          result.summary.existing_tables++;
        } else {
          result.tables.push({
            table_name: tableName,
            exists: false,
            error: 'Table not found in information_schema'
          });
          result.summary.missing_tables++;
          result.errors.push(`Table '${tableName}' not found`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        result.tables.push({
          table_name: tableName,
          exists: false,
          error: errorMessage
        });
        result.summary.missing_tables++;
        result.errors.push(`Error checking table '${tableName}': ${errorMessage}`);
      }
    }

    result.success = result.summary.missing_tables === 0;

    // Log results
    console.log(`✅ Tables found: ${result.summary.existing_tables}/${result.summary.total_tables}`);
    if (result.errors.length > 0) {
      console.log('❌ Errors:', result.errors);
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    result.errors.push(`Database verification failed: ${errorMessage}`);
    console.error('❌ Database verification failed:', error);
    return result;
  }
}

/**
 * Get column information for a table
 */
async function getTableColumns(tableName: string): Promise<string[]> {
  try {
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .order('ordinal_position');

    if (error) {
      console.warn(`Could not get columns for ${tableName}:`, error);
      return [];
    }

    return data?.map(col => col.column_name) || [];
  } catch (error) {
    console.warn(`Error getting columns for ${tableName}:`, error);
    return [];
  }
}

/**
 * Test basic reporting functionality
 */
export async function testReportingFunctionality(userId: string): Promise<boolean> {
  try {
    console.log('🧪 Testing basic reporting functionality...');

    // Test 1: Create a test report
    const { data: testReport, error: createError } = await supabase
      .from('reports')
      .insert({
        reporter_id: userId,
        reporter_username: 'test_user',
        target_type: 'user_behavior',
        target_user_id: userId,
        target_username: 'test_target',
        reason: 'spam',
        description: 'Test report for verification',
        severity: 'low',
        priority: 5,
        status: 'pending'
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Failed to create test report:', createError);
      return false;
    }

    console.log('✅ Test report created successfully');

    // Test 2: Query the report
    const { data: queriedReport, error: queryError } = await supabase
      .from('reports')
      .select('*')
      .eq('id', testReport.id)
      .single();

    if (queryError) {
      console.error('❌ Failed to query test report:', queryError);
      return false;
    }

    console.log('✅ Test report queried successfully');

    // Test 3: Clean up test report
    const { error: deleteError } = await supabase
      .from('reports')
      .delete()
      .eq('id', testReport.id);

    if (deleteError) {
      console.warn('⚠️ Failed to clean up test report:', deleteError);
    } else {
      console.log('✅ Test report cleaned up successfully');
    }

    return true;
  } catch (error) {
    console.error('❌ Reporting functionality test failed:', error);
    return false;
  }
}

/**
 * Run complete verification and testing
 */
export async function runCompleteVerification(userId?: string): Promise<VerificationResult> {
  console.log('🚀 Starting complete reporting system verification...');
  
  const dbResult = await verifyReportingDatabase();
  
  if (dbResult.success && userId) {
    const functionalityTest = await testReportingFunctionality(userId);
    if (!functionalityTest) {
      dbResult.errors.push('Functionality test failed');
      dbResult.success = false;
    }
  }
  
  console.log(dbResult.success ? '🎉 Verification completed successfully!' : '❌ Verification failed');
  return dbResult;
}
