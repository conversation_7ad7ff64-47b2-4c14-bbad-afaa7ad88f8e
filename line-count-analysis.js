#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const EXCLUDE_DIRS = [
  'node_modules',
  'dist',
  '.git',
  '.next',
  'build',
  'coverage',
  '.vscode',
  '.idea'
];

const EXCLUDE_FILES = [
  'package-lock.json',
  'yarn.lock',
  'bun.lockb',
  '.DS_Store',
  'Thumbs.db'
];

const FILE_EXTENSIONS = {
  'TypeScript/JavaScript': ['.ts', '.tsx', '.js', '.jsx'],
  'CSS/Styling': ['.css', '.scss', '.sass', '.less'],
  'HTML': ['.html', '.htm'],
  'SQL': ['.sql'],
  'Configuration': ['.json', '.toml', '.yaml', '.yml', '.config.js', '.config.ts'],
  'Markdown': ['.md'],
  'Other': []
};

class LineCounter {
  constructor() {
    this.stats = {
      totalLines: 0,
      totalFiles: 0,
      byFileType: {},
      byDirectory: {},
      fileDetails: []
    };

    // Initialize file type counters
    Object.keys(FILE_EXTENSIONS).forEach(type => {
      this.stats.byFileType[type] = { files: 0, lines: 0 };
    });
  }

  shouldExclude(filePath) {
    const parts = filePath.split(path.sep);
    
    // Check if any part of the path is in exclude dirs
    for (const excludeDir of EXCLUDE_DIRS) {
      if (parts.includes(excludeDir)) {
        return true;
      }
    }

    // Check if filename is in exclude files
    const fileName = path.basename(filePath);
    if (EXCLUDE_FILES.includes(fileName)) {
      return true;
    }

    return false;
  }

  getFileType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    
    for (const [type, extensions] of Object.entries(FILE_EXTENSIONS)) {
      if (type === 'Other') continue;
      if (extensions.includes(ext)) {
        return type;
      }
    }
    
    return 'Other';
  }

  countLinesInFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n').length;
      return lines;
    } catch (error) {
      console.warn(`Warning: Could not read file ${filePath}: ${error.message}`);
      return 0;
    }
  }

  analyzeDirectory(dirPath, relativePath = '') {
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const relativeItemPath = path.join(relativePath, item);
        
        if (this.shouldExclude(relativeItemPath)) {
          continue;
        }

        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          this.analyzeDirectory(fullPath, relativeItemPath);
        } else if (stat.isFile()) {
          this.analyzeFile(fullPath, relativeItemPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not read directory ${dirPath}: ${error.message}`);
    }
  }

  analyzeFile(filePath, relativePath) {
    const lines = this.countLinesInFile(filePath);
    const fileType = this.getFileType(filePath);
    const directory = path.dirname(relativePath) || 'root';
    
    // Update totals
    this.stats.totalLines += lines;
    this.stats.totalFiles += 1;
    
    // Update by file type
    this.stats.byFileType[fileType].files += 1;
    this.stats.byFileType[fileType].lines += lines;
    
    // Update by directory
    if (!this.stats.byDirectory[directory]) {
      this.stats.byDirectory[directory] = { files: 0, lines: 0 };
    }
    this.stats.byDirectory[directory].files += 1;
    this.stats.byDirectory[directory].lines += lines;
    
    // Store file details
    this.stats.fileDetails.push({
      path: relativePath,
      lines: lines,
      type: fileType,
      directory: directory
    });
  }

  generateReport() {
    console.log('📊 BookTalks Buddy Codebase Analysis');
    console.log('=====================================\n');
    
    // Overall stats
    console.log('📈 OVERALL STATISTICS');
    console.log(`Total Lines of Code: ${this.stats.totalLines.toLocaleString()}`);
    console.log(`Total Files: ${this.stats.totalFiles.toLocaleString()}\n`);
    
    // By file type
    console.log('📁 BREAKDOWN BY FILE TYPE');
    console.log('-------------------------');
    const sortedByType = Object.entries(this.stats.byFileType)
      .sort((a, b) => b[1].lines - a[1].lines)
      .filter(([_, data]) => data.files > 0);
    
    for (const [type, data] of sortedByType) {
      const percentage = ((data.lines / this.stats.totalLines) * 100).toFixed(1);
      console.log(`${type.padEnd(20)} ${data.lines.toLocaleString().padStart(8)} lines (${percentage}%) in ${data.files} files`);
    }
    console.log();
    
    // By directory
    console.log('📂 BREAKDOWN BY MAJOR DIRECTORIES');
    console.log('----------------------------------');
    const sortedByDir = Object.entries(this.stats.byDirectory)
      .sort((a, b) => b[1].lines - a[1].lines)
      .slice(0, 15); // Top 15 directories
    
    for (const [dir, data] of sortedByDir) {
      const percentage = ((data.lines / this.stats.totalLines) * 100).toFixed(1);
      console.log(`${dir.padEnd(30)} ${data.lines.toLocaleString().padStart(8)} lines (${percentage}%) in ${data.files} files`);
    }
    console.log();
    
    // Largest files
    console.log('📄 LARGEST FILES (Top 20)');
    console.log('--------------------------');
    const largestFiles = this.stats.fileDetails
      .sort((a, b) => b.lines - a.lines)
      .slice(0, 20);
    
    for (const file of largestFiles) {
      console.log(`${file.path.padEnd(50)} ${file.lines.toString().padStart(6)} lines [${file.type}]`);
    }
    console.log();
    
    // Source code breakdown
    console.log('💻 SOURCE CODE BREAKDOWN');
    console.log('-------------------------');
    const sourceTypes = ['TypeScript/JavaScript', 'CSS/Styling', 'HTML'];
    let sourceTotal = 0;
    
    for (const type of sourceTypes) {
      const data = this.stats.byFileType[type];
      sourceTotal += data.lines;
      console.log(`${type.padEnd(20)} ${data.lines.toLocaleString().padStart(8)} lines`);
    }
    console.log(`${'TOTAL SOURCE'.padEnd(20)} ${sourceTotal.toLocaleString().padStart(8)} lines`);
    console.log();
    
    // Configuration and docs
    console.log('📋 CONFIGURATION & DOCUMENTATION');
    console.log('---------------------------------');
    const configTypes = ['Configuration', 'SQL', 'Markdown'];
    let configTotal = 0;
    
    for (const type of configTypes) {
      const data = this.stats.byFileType[type];
      configTotal += data.lines;
      console.log(`${type.padEnd(20)} ${data.lines.toLocaleString().padStart(8)} lines`);
    }
    console.log(`${'TOTAL CONFIG/DOCS'.padEnd(20)} ${configTotal.toLocaleString().padStart(8)} lines`);
  }

  run() {
    console.log('🔍 Analyzing BookTalks Buddy codebase...\n');
    this.analyzeDirectory(__dirname);
    this.generateReport();
  }
}

// Run the analysis
const counter = new LineCounter();
counter.run();
