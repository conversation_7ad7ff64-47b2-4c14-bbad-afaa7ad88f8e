import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export default function ReportingTestPage() {
  const { user } = useAuth();
  const { toast } = useToast();

  console.log('ReportingTestPage rendering, user:', user);



  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert>
          <AlertDescription>
            Please log in to access the reporting test page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Reporting System Test</h1>
        <Button onClick={loadTestData} variant="outline">
          Refresh Data
        </Button>
      </div>

      {/* User Info */}
      <Card>
        <CardHeader>
          <CardTitle>User Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>User ID:</strong> {user.id}</p>
            <p><strong>Email:</strong> {user.email}</p>
            <div>
              <strong>Entitlements:</strong>
              <div className="flex flex-wrap gap-1 mt-1">
                {userEntitlements.map((entitlement) => (
                  <Badge key={entitlement} variant="secondary" className="text-xs">
                    {entitlement}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4 flex-wrap">
            <Button onClick={runDatabaseVerification} variant="outline">
              Verify Database
            </Button>

            <Button onClick={createTestReport}>
              Create Test Report
            </Button>

            <ReportButton
              targetType="user_behavior"
              targetUserId={user.id}
              targetTitle="Test User Profile"
              targetContent="This is a test profile for testing the reporting system."
            />
          </div>

          {verificationResult && (
            <div className="mt-4 p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Database Verification Results</h4>
              <div className="space-y-2 text-sm">
                <p>
                  <strong>Status:</strong>
                  <Badge variant={verificationResult.success ? "default" : "destructive"} className="ml-2">
                    {verificationResult.success ? "Success" : "Failed"}
                  </Badge>
                </p>
                <p><strong>Tables:</strong> {verificationResult.summary.existing_tables}/{verificationResult.summary.total_tables} found</p>
                {verificationResult.errors.length > 0 && (
                  <div>
                    <strong>Errors:</strong>
                    <ul className="list-disc list-inside mt-1 text-red-600">
                      {verificationResult.errors.map((error: string, index: number) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reports List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Visible Reports ({reports.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p>Loading reports...</p>
          ) : reports.length === 0 ? (
            <p className="text-gray-500">No reports found. You may not have moderation permissions or there are no reports to display.</p>
          ) : (
            <div className="space-y-4">
              {reports.map((report) => (
                <div key={report.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant={
                          report.severity === 'critical' ? 'destructive' :
                          report.severity === 'high' ? 'destructive' :
                          report.severity === 'medium' ? 'default' : 'secondary'
                        }>
                          {report.severity}
                        </Badge>
                        <Badge variant="outline">
                          {report.status}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          Priority: {report.priority}
                        </span>
                      </div>
                      <p className="font-medium">{report.reason.replace('_', ' ')}</p>
                      <p className="text-sm text-gray-600 mt-1">{report.description}</p>
                      <p className="text-xs text-gray-400 mt-2">
                        Created: {new Date(report.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Moderation Dashboard */}
      <Card>
        <CardHeader>
          <CardTitle>Moderation Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <ModerationDashboard />
        </CardContent>
      </Card>
    </div>
  );
}
